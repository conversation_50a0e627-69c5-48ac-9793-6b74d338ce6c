package com.example.castapp.ui.dialog

import android.app.AlertDialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.Toast
import com.example.castapp.R
import com.example.castapp.model.RemoteReceiverConnection
import com.example.castapp.utils.AppLog
import com.example.castapp.websocket.ControlMessage
import com.example.castapp.manager.RemoteReceiverManager
import com.example.castapp.manager.RemoteConnectionManager
import com.example.castapp.model.RemoteWindowConfigManager
import com.example.castapp.model.CastWindowInfo
import java.util.UUID

/**
 * 简单的媒体选择对话框
 */
class RemoteMediaSelectionDialog(
    private val context: Context,
    private val remoteReceiverConnection: RemoteReceiverConnection
) {
    
    // 媒体类型枚举
    enum class MediaType(val displayName: String, val messageType: String) {
        FRONT_CAMERA("前置摄像头", "front_camera"),
        REAR_CAMERA("后置摄像头", "rear_camera"),
        VIDEO("视频", "video"),
        PICTURE("图片", "picture"),
        TEXT("文本", "text")
    }
    
    // 管理器实例
    private val remoteReceiverManager = RemoteReceiverManager.getInstance()
    private val connectionManager = RemoteConnectionManager.getInstance()
    
    /**
     * 显示媒体选择对话框
     */
    fun show() {
        try {
            val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_media_selection, null)

            val dialog = AlertDialog.Builder(context)
                .setTitle("远程添加媒体窗口")
                .setView(dialogView)
                .setNegativeButton("取消", null)
                .create()

            setupClickListeners(dialogView, dialog)
            dialog.show()

            AppLog.d("【媒体选择对话框】对话框已显示")

        } catch (e: Exception) {
            AppLog.e("【媒体选择对话框】显示对话框失败", e)
            Toast.makeText(context, "显示对话框失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 设置点击监听器
     */
    private fun setupClickListeners(dialogView: View, dialog: AlertDialog) {
        dialogView.findViewById<LinearLayout>(R.id.btn_front_camera)?.setOnClickListener {
            handleMediaSelection(MediaType.FRONT_CAMERA)
            dialog.dismiss()
        }

        dialogView.findViewById<LinearLayout>(R.id.btn_rear_camera)?.setOnClickListener {
            handleMediaSelection(MediaType.REAR_CAMERA)
            dialog.dismiss()
        }

        dialogView.findViewById<LinearLayout>(R.id.btn_video)?.setOnClickListener {
            handleMediaSelection(MediaType.VIDEO)
            dialog.dismiss()
        }

        dialogView.findViewById<LinearLayout>(R.id.btn_picture)?.setOnClickListener {
            handleMediaSelection(MediaType.PICTURE)
            dialog.dismiss()
        }

        dialogView.findViewById<LinearLayout>(R.id.btn_text)?.setOnClickListener {
            handleMediaSelection(MediaType.TEXT)
            dialog.dismiss()
        }
    }
    

    
    /**
     * 处理媒体选择
     */
    private fun handleMediaSelection(mediaType: MediaType) {
        AppLog.d("【媒体选择对话框】选择了: ${mediaType.displayName}")
        
        try {
            if (isRealTimeSyncEnabled()) {
                // 实时同步开启：发送指令给接收端
                handleRealTimeSyncMode(mediaType)
            } else {
                // 实时同步关闭：在遥控端创建占位容器
                handleLocalContainerMode(mediaType)
            }
        } catch (e: Exception) {
            AppLog.e("【媒体选择对话框】处理媒体选择失败", e)
            Toast.makeText(context, "操作失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 处理实时同步模式
     */
    private fun handleRealTimeSyncMode(mediaType: MediaType) {
        AppLog.d("【媒体选择对话框】实时同步模式，发送指令到接收端")
        
        val mediaId = "${mediaType.messageType}_${UUID.randomUUID()}"
        val extraData = when (mediaType) {
            MediaType.FRONT_CAMERA, MediaType.REAR_CAMERA -> mapOf("cameraId" to mediaId)
            MediaType.TEXT -> mapOf("defaultText" to "默认文字")
            else -> emptyMap()
        }
        
        sendAddMediaMessage(mediaType.messageType, mediaType.displayName, extraData)
        Toast.makeText(context, "已发送${mediaType.displayName}创建指令", Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 处理本地容器模式
     */
    private fun handleLocalContainerMode(mediaType: MediaType) {
        when (mediaType) {
            MediaType.FRONT_CAMERA, MediaType.REAR_CAMERA -> {
                AppLog.d("【媒体选择对话框】本地模式，创建${mediaType.displayName}占位容器")
                createCameraPlaceholder(mediaType)
            }
            else -> {
                // 其他媒体类型暂时不支持本地模式
                Toast.makeText(context, "${mediaType.displayName}需要开启实时同步", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    /**
     * 创建摄像头占位容器
     */
    private fun createCameraPlaceholder(mediaType: MediaType) {
        try {
            // 🎥 根本解决方案：模拟摄像头容器使用独特但简洁的ID
            // 格式：{camera_type}_placeholder，如 "front_camera_placeholder"
            val cameraId = "${mediaType.messageType}_placeholder"

            // 🎯 关键修复：检查是否已存在相同的摄像头占位容器
            val cache = com.example.castapp.manager.RemoteWindowInfoCache.getInstance()
            val existingWindows = cache.loadWindowInfo(context, remoteReceiverConnection.id)

            // 检查是否已存在相同connectionId的窗口
            val existingWindow = existingWindows.find { it.connectionId == cameraId }
            if (existingWindow != null) {
                AppLog.d("【媒体选择对话框】${mediaType.displayName}占位容器已存在，跳过创建: $cameraId")
                Toast.makeText(context, "${mediaType.displayName}占位容器已存在", Toast.LENGTH_SHORT).show()
                return
            }

            // 获取接收端屏幕分辨率
            val receiverScreenResolution = getReceiverScreenResolution()
            val remoteControlScale = getRemoteControlScale()

            // 计算容器尺寸
            val containerWidth = (receiverScreenResolution.first * 0.4 * remoteControlScale).toInt()
            val containerHeight = (receiverScreenResolution.second * 0.4 * remoteControlScale).toInt()

            // 创建窗口信息
            val windowInfo = CastWindowInfo(
                connectionId = cameraId,
                ipAddress = remoteReceiverConnection.ipAddress,
                port = remoteReceiverConnection.port,
                isActive = true,
                deviceName = mediaType.displayName,
                positionX = 100f,
                positionY = 100f,
                isVisible = true,
                isBorderEnabled = true,
                borderColor = 0xFFFF0000.toInt(),
                borderWidth = 4f,
                cornerRadius = 8f,
                baseWindowWidth = containerWidth,
                baseWindowHeight = containerHeight
            )

            // 🎯 核心修复：获取现有窗口的当前实际位置，而不是使用缓存中的原始位置
            val updatedExistingWindowsWithCurrentPositions = getCurrentWindowPositions(existingWindows)

            // 🎯 层级修复：新摄像头窗口应该显示在最上层（zOrder=1）
            // 现有窗口的 zOrder 需要增加，为新窗口让出最上层位置
            val newZOrder = 1
            val windowInfoWithCorrectZOrder = windowInfo.copy(zOrder = newZOrder)

            // 更新现有窗口的层级，所有现有窗口的 zOrder 增加1
            val updatedExistingWindows = updatedExistingWindowsWithCurrentPositions.map { existingWindow ->
                existingWindow.copy(zOrder = existingWindow.zOrder + 1)
            }

            AppLog.d("【媒体选择对话框】新摄像头窗口层级: $newZOrder (最上层)")
            AppLog.d("【媒体选择对话框】现有窗口层级已调整: ${updatedExistingWindows.map { "${it.getDisplayTextWithDevice()}(z=${it.zOrder})" }}")

            // 创建完整的窗口列表（更新后的现有窗口 + 新摄像头窗口）
            val completeWindowList = updatedExistingWindows.toMutableList()
            completeWindowList.add(windowInfoWithCorrectZOrder)

            AppLog.d("【媒体选择对话框】原有窗口: ${existingWindows.size} 个")
            AppLog.d("【媒体选择对话框】添加新摄像头窗口后: ${completeWindowList.size} 个")
            AppLog.d("【媒体选择对话框】完整窗口列表层级: ${completeWindowList.sortedBy { it.zOrder }.map { "${it.getDisplayTextWithDevice()}(z=${it.zOrder})" }}")

            // 保存完整的窗口列表到配置管理器
            val configManager = RemoteWindowConfigManager.getInstance()
            configManager.updateFromReceiverData(remoteReceiverConnection.id, completeWindowList)

            // 保存完整的窗口列表到缓存
            cache.saveWindowInfo(context, remoteReceiverConnection.id, completeWindowList)

            // 🎯 关键修复：使用完整的窗口列表触发UI更新
            triggerVisualizationUpdate(completeWindowList)

            Toast.makeText(context, "已创建${mediaType.displayName}占位容器", Toast.LENGTH_SHORT).show()
            AppLog.d("【媒体选择对话框】${mediaType.displayName}占位容器创建成功")
            
        } catch (e: Exception) {
            AppLog.e("【媒体选择对话框】创建占位容器失败", e)
            Toast.makeText(context, "创建容器失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 发送添加媒体消息
     */
    private fun sendAddMediaMessage(mediaType: String, displayName: String, extraData: Map<String, Any> = emptyMap()) {
        try {
            val client = connectionManager.getReceiverClient(remoteReceiverConnection.id)
            if (client != null && client.getConnectionStatus()) {
                val actualConnectionId = client.getActualConnectionId()

                val messageData = mutableMapOf<String, Any>(
                    "mediaType" to mediaType,
                    "displayName" to displayName,
                    "timestamp" to System.currentTimeMillis()
                )
                messageData.putAll(extraData)

                val controlMessage = ControlMessage(
                    type = "add_media",
                    connectionId = actualConnectionId,
                    data = messageData
                )

                if (client.sendMessage(controlMessage)) {
                    AppLog.d("【媒体选择对话框】发送添加${displayName}指令成功")
                    Toast.makeText(context, "已发送${displayName}创建指令", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(context, "发送指令失败", Toast.LENGTH_SHORT).show()
                }
            } else {
                Toast.makeText(context, "设备未连接，无法添加${displayName}", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            AppLog.e("【媒体选择对话框】发送添加${displayName}指令失败", e)
            Toast.makeText(context, "发送指令失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 检查实时同步状态
     */
    private fun isRealTimeSyncEnabled(): Boolean {
        val sharedPrefs = context.getSharedPreferences("remote_window_settings", Context.MODE_PRIVATE)
        val syncStateKey = "sync_enabled_${remoteReceiverConnection.id}"
        return sharedPrefs.getBoolean(syncStateKey, false)
    }
    
    /**
     * 获取接收端屏幕分辨率
     */
    private fun getReceiverScreenResolution(): Pair<Int, Int> {
        return try {
            val manager = RemoteReceiverManager.getInstance()
            manager.getCachedScreenResolution(remoteReceiverConnection.id) ?: Pair(1080, 1920)
        } catch (e: Exception) {
            AppLog.e("【媒体选择对话框】获取屏幕分辨率失败", e)
            Pair(1080, 1920)
        }
    }
    
    /**
     * 获取遥控端窗口缩放倍数
     * 🎯 简化：使用默认缩放倍数，避免复杂计算
     */
    private fun getRemoteControlScale(): Double {
        return try {
            // 简化实现：使用固定的缩放倍数
            // 这个值可以根据实际需要调整
            1.0
        } catch (e: Exception) {
            AppLog.e("【媒体选择对话框】获取缩放倍数失败", e)
            1.0
        }
    }

    /**
     * 🎯 核心修复：获取现有窗口的当前实际变换状态
     * 从可视化窗口中获取用户操作后的实际变换状态（位置、缩放、旋转等），而不是使用缓存中的原始状态
     */
    private fun getCurrentWindowPositions(existingWindows: List<CastWindowInfo>): List<CastWindowInfo> {
        return try {
            // 查找当前活跃的控制对话框
            val activeControlDialog = findActiveControlDialog()
            if (activeControlDialog == null) {
                AppLog.w("【变换修复】未找到活跃的控制对话框，使用原始状态")
                return existingWindows
            }

            // 获取可视化窗口
            val visualizationView = activeControlDialog.getWindowVisualizationView()
            if (visualizationView == null) {
                AppLog.w("【变换修复】未找到可视化窗口，使用原始状态")
                return existingWindows
            }

            AppLog.d("【变换修复】开始获取 ${existingWindows.size} 个窗口的当前变换状态")

            // 更新每个窗口的变换信息
            existingWindows.map { windowInfo ->
                val currentTransform = getCurrentWindowTransform(visualizationView, windowInfo.connectionId)
                if (currentTransform != null) {
                    AppLog.d("【变换修复】窗口 ${windowInfo.connectionId} 当前变换: 位置(${currentTransform.x}, ${currentTransform.y}), 缩放${currentTransform.scale}, 旋转${currentTransform.rotation}°, 裁剪${currentTransform.isCropping}")
                    AppLog.d("【变换修复】窗口 ${windowInfo.connectionId} 原始变换: 位置(${windowInfo.positionX}, ${windowInfo.positionY}), 缩放${windowInfo.scaleFactor}, 旋转${windowInfo.rotationAngle}°, 裁剪${windowInfo.isCropping}")

                    val updatedWindowInfo = windowInfo.copy(
                        positionX = currentTransform.x,
                        positionY = currentTransform.y,
                        scaleFactor = currentTransform.scale,
                        rotationAngle = currentTransform.rotation,
                        isMirrored = currentTransform.isMirrored,
                        isCropping = currentTransform.isCropping,
                        cropRectRatio = currentTransform.cropRectRatio
                    )

                    // 🎯 调试：确认裁剪参数已正确更新
                    AppLog.d("【裁剪参数确认】窗口 ${windowInfo.connectionId} 更新后的裁剪状态:")
                    AppLog.d("  更新后 isCropping = ${updatedWindowInfo.isCropping}")
                    AppLog.d("  更新后 cropRectRatio = ${updatedWindowInfo.cropRectRatio}")

                    updatedWindowInfo
                } else {
                    AppLog.d("【变换修复】窗口 ${windowInfo.connectionId} 未找到当前变换状态，保持原始状态")
                    windowInfo
                }
            }
        } catch (e: Exception) {
            AppLog.e("【变换修复】获取当前窗口变换状态失败", e)
            existingWindows
        }
    }

    /**
     * 🎯 变换状态数据类
     */
    private data class WindowTransformState(
        val x: Float,
        val y: Float,
        val scale: Float,
        val rotation: Float,
        val isMirrored: Boolean,
        val isCropping: Boolean,
        val cropRectRatio: android.graphics.RectF?
    )

    /**
     * 🎯 获取指定窗口的当前完整变换状态
     */
    private fun getCurrentWindowTransform(visualizationView: com.example.castapp.ui.view.WindowContainerVisualizationView, connectionId: String): WindowTransformState? {
        return try {
            // 遍历可视化窗口的子View，查找对应的窗口容器
            for (i in 0 until visualizationView.childCount) {
                val child = visualizationView.getChildAt(i)
                if (child is com.example.castapp.ui.view.WindowVisualizationContainerView) {
                    val windowData = child.getWindowData()
                    if (windowData?.connectionId == connectionId) {
                        // 获取当前View的变换状态
                        val currentX = child.x
                        val currentY = child.y
                        val currentScale = child.scaleX  // 假设scaleX和scaleY相同
                        val currentRotation = child.rotation
                        val isMirrored = windowData.isMirrored
                        val isCropping = windowData.isCropping
                        val cropRectRatio = windowData.cropRectRatio

                        // 🎯 调试：详细输出裁剪参数获取情况
                        AppLog.d("【裁剪参数调试】窗口 $connectionId 裁剪参数获取:")
                        AppLog.d("  windowData.isCropping = $isCropping")
                        AppLog.d("  windowData.cropRectRatio = $cropRectRatio")

                        // 将遥控端坐标转换为接收端坐标
                        val (actualX, actualY) = com.example.castapp.utils.WindowScaleCalculator.convertRemoteToActualCoordinates(
                            remoteX = currentX,
                            remoteY = currentY,
                            remoteControlScale = windowData.remoteControlScale
                        )

                        // 🎯 关键修复：计算实际的缩放因子
                        // 当前View的缩放 = 基础缩放（来自接收端） × 手势缩放（遥控端操作）
                        // 需要将总缩放转换回接收端的缩放因子
                        val baseScaleFactor = windowData.scaleFactor  // 来自接收端的基础缩放
                        val gestureScaleFactor = currentScale  // 遥控端的手势缩放
                        val actualScaleFactor = baseScaleFactor * gestureScaleFactor

                        AppLog.d("【变换转换】窗口 $connectionId:")
                        AppLog.d("  位置: 遥控端($currentX, $currentY) -> 接收端($actualX, $actualY)")
                        AppLog.d("  缩放: 基础${baseScaleFactor} × 手势${gestureScaleFactor} = 实际${actualScaleFactor}")
                        AppLog.d("  旋转: ${currentRotation}°")
                        AppLog.d("  镜像: $isMirrored")
                        AppLog.d("  裁剪: $isCropping, 区域: $cropRectRatio")

                        return WindowTransformState(
                            x = actualX,
                            y = actualY,
                            scale = actualScaleFactor,
                            rotation = currentRotation,
                            isMirrored = isMirrored,
                            isCropping = isCropping,
                            cropRectRatio = cropRectRatio
                        )
                    }
                }
            }
            null
        } catch (e: Exception) {
            AppLog.e("【变换修复】获取窗口 $connectionId 当前变换状态失败", e)
            null
        }
    }

    /**
     * 🎯 查找当前活跃的控制对话框
     */
    private fun findActiveControlDialog(): RemoteReceiverControlDialog? {
        return try {
            // 通过FragmentManager查找当前显示的RemoteReceiverControlDialog
            val activity = context as? androidx.fragment.app.FragmentActivity
            if (activity != null) {
                val fragmentManager = activity.supportFragmentManager
                val fragments = fragmentManager.fragments

                for (fragment in fragments) {
                    if (fragment is RemoteReceiverControlDialog && fragment.isVisible) {
                        AppLog.d("【位置修复】找到活跃的控制对话框: ${fragment.javaClass.simpleName}")
                        return fragment
                    }
                }
            }

            AppLog.w("【位置修复】未找到活跃的控制对话框")
            null
        } catch (e: Exception) {
            AppLog.e("【位置修复】查找活跃控制对话框失败", e)
            null
        }
    }

    /**
     * 触发可视化更新
     * 🎯 关键修复：通知RemoteReceiverControlDialog更新窗口显示
     */
    private fun triggerVisualizationUpdate(windowInfoList: List<CastWindowInfo>) {
        try {
            AppLog.d("【媒体选择对话框】触发可视化更新: ${windowInfoList.size} 个窗口")

            // 获取RemoteReceiverManager实例
            val manager = RemoteReceiverManager.getInstance()

            // 获取当前活跃的控制对话框
            val controlDialog = manager.getActiveControlDialog(remoteReceiverConnection.id)

            if (controlDialog != null) {
                // 调用控制对话框的updateWindowVisualization方法
                controlDialog.updateWindowVisualization(windowInfoList, shouldRequestData = false)
                AppLog.d("【媒体选择对话框】已通知控制对话框更新可视化")
            } else {
                AppLog.w("【媒体选择对话框】未找到活跃的控制对话框: ${remoteReceiverConnection.id}")
            }

        } catch (e: Exception) {
            AppLog.e("【媒体选择对话框】触发可视化更新失败", e)
        }
    }
}
